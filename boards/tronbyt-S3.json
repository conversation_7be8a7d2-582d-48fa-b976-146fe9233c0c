{"build": {"core": "esp32", "f_cpu": "240000000L", "f_flash": "80000000L", "flash_mode": "qio", "psram_type": "opi", "mcu": "esp32s3", "partitions": "boards/default_8MB.csv", "variant": "esp32s3", "esp-idf": {"sdkconfig_path": "sdkconfig.tronbyt-S3.defaults"}, "extra_flags": ["-DBOARD_HAS_PSRAM"]}, "connectivity": ["wifi", "bluetooth"], "debug": {"default_tool": "esp-builtin", "onboard_tools": ["esp-builtin"], "openocd_target": "esp32s3.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "Espressif ESP32-S3-DevKitC-1-N16R8V (16 MB QD, 8MB PSRAM)", "upload": {"flash_size": "16MB", "maximum_ram_size": 327680, "maximum_size": 16777216, "require_upload_port": true, "speed": 921600}, "url": "https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/hw-reference/esp32s3/user-guide-devkitc-1.html", "vendor": "E<PERSON>ress<PERSON>", "docs": "https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/hw-reference/esp32s3/user-guide-devkitc-1.html"}