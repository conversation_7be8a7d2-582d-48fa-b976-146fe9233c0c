{"build": {"core": "esp32", "f_cpu": "240000000L", "f_flash": "40000000L", "flash_mode": "dio", "mcu": "esp32", "partitions": "boards/default_8MB.csv", "variant": "esp32", "esp-idf": {"sdkconfig_path": "sdkconfig"}}, "connectivity": ["wifi", "bluetooth", "ethernet", "can"], "debug": {"default_tool": "ftdi", "onboard_tools": ["ftdi"], "openocd_board": "esp32-wrover.cfg"}, "frameworks": ["a<PERSON><PERSON><PERSON>", "espidf"], "name": "Espressif ESP32 Dev Module", "upload": {"flash_size": "8MB", "maximum_ram_size": 327680, "maximum_size": 8388608, "require_upload_port": true, "speed": 921600, "protocols": ["esptool", "ftdi"]}, "url": "https://tidbyt.com", "vendor": "T<PERSON><PERSON><PERSON>"}