name: Build and Release

on:
  push:
    branches:
      - main
      - modernize
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pip
            ~/.platformio/.cache
          key: ${{ runner.os }}-pio

      - uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install PlatformIO Core
        run: pip install platformio==6.1.18

      - name: Build PlatformIO Project
        run: pio run
