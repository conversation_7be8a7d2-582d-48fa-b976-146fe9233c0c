#!/usr/bin/env python3
"""
Post-upload script for PlatformIO

This script runs after successful firmware upload and performs the following actions:
1. Renames secrets.json to secrets.json.injected to indicate successful injection
2. Preserves previous injected files with timestamps
3. Provides clear feedback about the secrets injection status

Usage:
- Automatically runs after: pio run -e <env> --target upload
- Helps track which secrets have been injected into firmware
- Prevents accidental reuse of the same secrets file

To reuse secrets: cp secrets.json.injected secrets.json
"""

import os
import shutil
from datetime import datetime

Import("env")


def rename_secrets_after_upload(*args, **kwargs):
    """
    Post-upload action to rename secrets.json to secrets.json.injected
    This indicates that the secrets have been successfully injected into the firmware.
    """
    secrets_file = "secrets.json"
    injected_file = "secrets.json.injected"
    
    if os.path.exists(secrets_file):
        try:
            # Add timestamp to the injected file for tracking
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            timestamped_file = f"secrets.json.injected.{timestamp}"
            
            # If an injected file already exists, rename it with timestamp
            if os.path.exists(injected_file):
                print(f"Previous {injected_file} found, renaming to {timestamped_file}")
                shutil.move(injected_file, timestamped_file)
            
            # Rename the current secrets.json to secrets.json.injected
            shutil.move(secrets_file, injected_file)
            print(f"✓ Successfully renamed {secrets_file} to {injected_file}")
            print(f"  This indicates secrets have been injected into the firmware.")
            print(f"  To use secrets again, copy {injected_file} back to {secrets_file}")
            
        except Exception as e:
            print(f"Warning: Failed to rename {secrets_file}: {e}")
    else:
        print(f"No {secrets_file} file found - nothing to rename")


def main():
    """
    Main function that sets up the post-upload action
    """
    # Add post-upload action to rename secrets file
    env.AddPostAction("upload", rename_secrets_after_upload)


# Execute main function
main()
