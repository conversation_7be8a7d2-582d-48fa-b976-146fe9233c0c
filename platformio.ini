; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env]
platform = espressif32 @ 6.11.0
framework = espidf
monitor_speed = 115200
; monitor_rts = 0
; monitor_dtr = 0
extra_scripts = 
	pre:extra_scripts/pre.py
	extra_scripts/reset.py
monitor_filters = 
	direct


;    _____ ______ _   _       __ 
;   / ____|  ____| \ | |     /_ |
;  | |  __| |__  |  \| |______| |
;  | | |_ |  __| | . ` |______| |
;  | |__| | |____| |\  |      | |
;   \_____|______|_| \_|      |_|                                                           
;###################################################################################
;###################################################################################
[env:tidbyt-gen1]
board = tidbyt
board_build.partitions = boards/default_8mb.csv
board_build.cmake_extra_args = 
    -DSDKCONFIG_DEFAULTS="sdkconfig.defaults;"
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D HTTP_BUFFER_SIZE_MAX=220000
	-D HTTP_BUFFER_SIZE_DEFAULT=100000
lib_deps = 
	https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
	https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320

[env:tidbyt-gen1_swap]
board = tidbyt
board_build.partitions = boards/default_8mb.csv
board_build.cmake_extra_args = 
    -DSDKCONFIG_DEFAULTS="sdkconfig.defaults;"
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D SWAP_COLORS
	-D HTTP_BUFFER_SIZE_MAX=220000
	-D HTTP_BUFFER_SIZE_DEFAULT=100000
lib_deps = 
	https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
	https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320

;###################################################################################
;###################################################################################



;    _____ ______ _   _      ___  
;   / ____|  ____| \ | |    |__ \ 
;  | |  __| |__  |  \| |______ ) |
;  | | |_ |  __| | . ` |______/ / 
;  | |__| | |____| |\  |     / /_ 
;   \_____|______|_| \_|    |____|                                
;###################################################################################
;###################################################################################
[env:tidbyt-gen2]
board = gen2
board_build.partitions = boards/default_8mb.csv
board_build.cmake_extra_args = 
    -DSDKCONFIG_DEFAULTS="sdkconfig.defaults;"
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D TIDBYT_GEN2
	-D NO_INVERT_CLOCK_PHASE
	-D BOOT_WEBP_TRONBYT ; not required as this is the default
	-D HTTP_BUFFER_SIZE_MAX=220000
	-D HTTP_BUFFER_SIZE_DEFAULT=100000
lib_deps = 
	https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
	https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320
;###################################################################################
;###################################################################################



;   _____ _______   ______ _______ _____ _____ _  ________ _____  
;  |  __ \_   _\ \ / / __ \__   __|_   _/ ____| |/ /  ____|  __ \ 
;  | |__) || |  \ V / |  | | | |    | || |    | ' /| |__  | |__) |
;  |  ___/ | |   > <| |  | | | |    | || |    |  < |  __| |  _  / 
;  | |    _| |_ / . \ |__| | | |   _| || |____| . \| |____| | \ \ 
;  |_|   |_____/_/ \_\____/  |_|  |_____\_____|_|\_\______|_|  \_\ 4MB flash, no PSRAM
;###################################################################################
;###################################################################################
[env:pixoticker]
board = wroom32
board_build.partitions = boards/max_app_4mb.csv
board_build.flash_size = 4MB
board_build.cmake_extra_args = 
    -DSDKCONFIG_DEFAULTS="sdkconfig.defaults;sdkconfig.pixoticker.defaults"
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D SWAP_COLORS
	-D PIXOTICKER
	-D HTTP_BUFFER_SIZE_MAX=30000
	-D HTTP_BUFFER_SIZE_DEFAULT=10000
	-D BOOT_WEBP_PARROT ; smaller size boot for pixoticker
	
lib_deps = 
	https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
	https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320



;   _______              _           _          _____ ____  
;  |__   __|            | |         | |        / ____|___ \ 
;     | |_ __ ___  _ __ | |__  _   _| |_ _____| (___   __) |
;     | | '__/ _ \| '_ \| '_ \| | | | __|______\___ \ |__ < 
;     | | | | (_) | | | | |_) | |_| | |_       ____) |___) |
;     |_|_|  \___/|_| |_|_.__/ \__, |\__|     |_____/|____/ 
;                               __/ |                       
;                              |___/                        
;###################################################################################
;###################################################################################
[env:tronbyt-s3]
board = tronbyt-S3
board_build.partitions = boards/default_8mb.csv
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D TRONBYT_S3
	-D NO_INVERT_CLOCK_PHASE
	-D BOOT_WEBP_TRONBYT ; not required as this is the default
	-D HTTP_BUFFER_SIZE_MAX=220000
	-D HTTP_BUFFER_SIZE_DEFAULT=100000
	-Wno-deprecated-declarations
	-Wno-missing-field-initializers
lib_deps = 
	https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
	https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320


;   __  __       _        _        _____           _        _    _____ ____  
;  |  \/  |     | |      (_)      |  __ \         | |      | |  / ____|___ \ 
;  | \  / | __ _| |_ _ __ ___  __ | |__) |__  _ __| |_ __ _| | | (___   __) |
;  | |\/| |/ _` | __| '__| \ \/ / |  ___/ _ \| '__| __/ _` | |  \___ \ |__ < 
;  | |  | | (_| | |_| |  | |>  <  | |  | (_) | |  | || (_| | |  ____) |___) |
;  |_|  |_|\__,_|\__|_|  |_/_/\_\ |_|   \___/|_|   \__\__,_|_| |_____/|____/                                                                         
                                                                                                                                                                                                                                                                                                        
;###################################################################################
;###################################################################################
[env:matrixportal-s3]
board = adafruit_matrixportal_esp32s3
board_build.partitions = boards/default_8mb.csv
build_flags = 
	-D NO_GFX
	-D NO_FAST_FUNCTIONS
	-D MATRIXPORTALS3
	-D NO_INVERT_CLOCK_PHASE
	-D BOOT_WEBP_TRONBYT ; not required as this is the default
	-D HTTP_BUFFER_SIZE_MAX=220000
	-D HTTP_BUFFER_SIZE_DEFAULT=100000
lib_deps = 
		https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
		https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327 ; commit as of 20250320


;   _______              _                  _____ ____     __          ___     _      
;  |__   __|            | |                / ____|___ \    \ \        / (_)   | |     
;     | |_ __ ___  _ __ | |__  _   _ _____| (___   __) |____\ \  /\  / / _  __| | ___ 
;     | | '__/ _ \| '_ \| '_ \| | | |______\___ \ |__ <______\ \/  \/ / | |/ _` |/ _ \
;     | | | | (_) | | | | |_) | |_| |      ____) |___) |      \  /\  /  | | (_| |  __/
;     |_|_|  \___/|_| |_|_.__/ \__, |     |_____/|____/        \/  \/   |_|\__,_|\___|
;                               __/ |                                                 
;                              |___/                                                  

;###################################################################################
;###################################################################################
[env:tronbyt-s3-wide]
board = tronbyt-S3
board_build.partitions = boards/default_8mb.csv
build_flags = 
    -D NO_GFX
    -D NO_FAST_FUNCTIONS
    -D TRONBYT_S3
    -D TRONBYT_S3_WIDE
    -D NO_INVERT_CLOCK_PHASE
    -D BOOT_WEBP_TRONBYT
    -D HTTP_BUFFER_SIZE_MAX=220000
    -D HTTP_BUFFER_SIZE_DEFAULT=100000
    -Wno-deprecated-declarations
    -Wno-missing-field-initializers
lib_deps = 
    https://github.com/webmproject/libwebp.git#1d86819f49edc8237fa2b844543081bcb8ef8a92
    https://github.com/mrcodetastic/ESP32-HUB75-MatrixPanel-DMA.git#aed04adfcda1838bf85c629a8c3b560919b3a327
;###################################################################################
;###################################################################################
